<mxfile host="app.diagrams.net" modified="2024-01-15T10:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="OAAL Framework" id="oaal-framework">
    <mxGraphModel dx="2074" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 背景和面板分割线 -->
        <mxCell id="panel-divider-1" value="" style="shape=line;strokeWidth=2;strokeColor=#CCCCCC;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="750" y="50" width="2" height="720" as="geometry" />
        </mxCell>
        <mxCell id="panel-divider-2" value="" style="shape=line;strokeWidth=2;strokeColor=#CCCCCC;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="750" y="400" width="400" height="2" as="geometry" />
        </mxCell>
        
        <!-- 面板标题 -->
        <mxCell id="panel-a-title" value="a. OAAL Closed-Loop Learning Architecture" style="text;fontSize=14;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="50" y="20" width="350" height="25" as="geometry" />
        </mxCell>
        <mxCell id="panel-b-title" value="b. Generative Sequence Scheduler" style="text;fontSize=14;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="770" y="20" width="300" height="25" as="geometry" />
        </mxCell>
        <mxCell id="panel-c-title" value="c. Collective Wisdom Evolution" style="text;fontSize=14;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="770" y="420" width="300" height="25" as="geometry" />
        </mxCell>

        <!-- ========== 面板A: 主架构 ========== -->
        
        <!-- 环境输入模块 -->
        <mxCell id="env-input" value="Environment Input&lt;br&gt;&lt;br&gt;• Local State s&lt;sub&gt;j&lt;/sub&gt;&lt;sup&gt;local&lt;/sup&gt;&lt;br&gt;• Task Queue Q&lt;sub&gt;j&lt;/sub&gt;&lt;br&gt;• Neighbor States N&lt;sub&gt;j&lt;/sub&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=2;fontSize=10;align=left;verticalAlign=top;spacingLeft=10;spacingTop=5;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="140" height="100" as="geometry" />
        </mxCell>

        <!-- 策略域模块 -->
        <mxCell id="strategy-domain" value="Strategy Domain&lt;br&gt;π&lt;sub&gt;θ&lt;sub&gt;d&lt;/sub&gt;&lt;/sub&gt;" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fillColor=#FEF3E2;strokeColor=#F18F01;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="220" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- GAT协同模块 -->
        <mxCell id="gat-module" value="Graph Attention Network&lt;br&gt;(GAT)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F8F0;strokeColor=#4CAF50;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="230" y="80" width="140" height="60" as="geometry" />
        </mxCell>

        <!-- 协同状态表示 -->
        <mxCell id="coord-state" value="Coordinated&lt;br&gt;State Representation" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F0F8F0;strokeColor=#4CAF50;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="250" y="160" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- 智能体心智模块 (核心) -->
        <mxCell id="agent-mind" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="420" y="120" width="200" height="160" as="geometry" />
        </mxCell>
        <mxCell id="agent-title" value="LEO Agent Policy π&lt;sub&gt;θ&lt;sub&gt;j&lt;/sub&gt;&lt;/sub&gt;" style="text;fontSize=12;fontStyle=1;fontColor=#2E86AB;" vertex="1" parent="1">
          <mxGeometry x="430" y="130" width="180" height="20" as="geometry" />
        </mxCell>
        <mxCell id="transformer-icon" value="🧠 Transformer&lt;br&gt;Encoder-Decoder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#2E86AB;strokeWidth=1;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="450" y="160" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sequence-output" value="Generated Schedule&lt;br&gt;Sequence a&lt;sub&gt;j&lt;/sub&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#2E86AB;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="450" y="230" width="140" height="35" as="geometry" />
        </mxCell>

        <!-- 环境交互模块 -->
        <mxCell id="environment" value="Environment&lt;br&gt;Interaction" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#A4A4A4;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="160" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 奖励模块 -->
        <mxCell id="reward" value="Reward r&lt;sub&gt;j&lt;/sub&gt;" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#A4A4A4;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="690" y="240" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 经验回放池 -->
        <mxCell id="experience-memory" value="Experience&lt;br&gt;Memory" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#F5F5F5;strokeColor=#A4A4A4;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="680" y="300" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- 双通道学习模块 -->
        <mxCell id="dual-learning" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAFAFA;strokeColor=#666666;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="320" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="dual-title" value="Dual-Channel Learning" style="text;fontSize=11;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="430" y="330" width="180" height="15" as="geometry" />
        </mxCell>

        <!-- RL通道 -->
        <mxCell id="rl-channel" value="RL Channel&lt;br&gt;L&lt;sub&gt;RL&lt;/sub&gt; (PPO)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="430" y="350" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 蒸馏通道 -->
        <mxCell id="distill-channel" value="Distillation&lt;br&gt;L&lt;sub&gt;distill&lt;/sub&gt; (KL)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEF3E2;strokeColor=#F18F01;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="530" y="350" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 总损失 -->
        <mxCell id="total-loss" value="Total Loss&lt;br&gt;L = (1-λ)L&lt;sub&gt;RL&lt;/sub&gt; + λL&lt;sub&gt;distill&lt;/sub&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#333333;strokeWidth=2;fontSize=9;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="400" width="140" height="30" as="geometry" />
        </mxCell>

        <!-- 策略域演进模块 -->
        <mxCell id="domain-evolution" value="Strategy Domain&lt;br&gt;Evolution" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEF3E2;strokeColor=#F18F01;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="230" y="320" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 性能评估 -->
        <mxCell id="performance-scorer" value="Performance&lt;br&gt;Scorer P&lt;sub&gt;j&lt;/sub&gt;&lt;sup&gt;d&lt;/sup&gt;" style="diamond;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="350" y="380" width="80" height="60" as="geometry" />
        </mxCell>

        <!-- ========== 面板B: 生成式序列调度器 ========== -->
        
        <!-- 任务队列输入 -->
        <mxCell id="task-queue-input" value="Task Queue&lt;br&gt;📋📋📋" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#A4A4A4;strokeWidth=1;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="780" y="60" width="80" height="50" as="geometry" />
        </mxCell>

        <!-- Transformer编码器 -->
        <mxCell id="transformer-encoder" value="Transformer&lt;br&gt;Encoder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="890" y="60" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- 上下文嵌入 -->
        <mxCell id="context-embed" value="Contextual&lt;br&gt;Embeddings" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="910" y="130" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 自回归解码器 -->
        <mxCell id="autoregressive-decoder" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F8F0;strokeColor=#4CAF50;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="780" y="190" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="decoder-title" value="Auto-regressive Decoder" style="text;fontSize=11;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="790" y="200" width="180" height="15" as="geometry" />
        </mxCell>

        <!-- 解码步骤 -->
        <mxCell id="decode-t1" value="t₁" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="790" y="220" width="30" height="25" as="geometry" />
        </mxCell>
        <mxCell id="decode-t2" value="t₂" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="830" y="220" width="30" height="25" as="geometry" />
        </mxCell>
        <mxCell id="decode-t3" value="t₃" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="870" y="220" width="30" height="25" as="geometry" />
        </mxCell>

        <!-- 动作掩码 -->
        <mxCell id="action-mask" value="Action&lt;br&gt;Masking ❌" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="920" y="220" width="50" height="40" as="geometry" />
        </mxCell>

        <!-- 完整调度序列 -->
        <mxCell id="complete-schedule" value="Complete Schedule&lt;br&gt;a&lt;sub&gt;j&lt;/sub&gt; = (a&lt;sub&gt;j&lt;/sub&gt;&lt;sup&gt;1&lt;/sup&gt;, a&lt;sub&gt;j&lt;/sub&gt;&lt;sup&gt;2&lt;/sup&gt;, ...)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="300" width="160" height="50" as="geometry" />
        </mxCell>

        <!-- ========== 面板C: 集体智慧演进 ========== -->
        
        <!-- 多智能体策略输入 -->
        <mxCell id="agent-policies" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F5F5F5;strokeColor=#A4A4A4;strokeWidth=2;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="780" y="460" width="120" height="100" as="geometry" />
        </mxCell>
        <mxCell id="policies-title" value="Agent Policies" style="text;fontSize=11;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="790" y="470" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- 个体策略 -->
        <mxCell id="policy-j" value="θ&lt;sub&gt;j&lt;/sub&gt; (P&lt;sub&gt;j&lt;/sub&gt;&lt;sup&gt;d&lt;/sup&gt;=0.95)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=1;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="790" y="490" width="45" height="25" as="geometry" />
        </mxCell>
        <mxCell id="policy-k" value="θ&lt;sub&gt;k&lt;/sub&gt; (P&lt;sub&gt;k&lt;/sub&gt;&lt;sup&gt;d&lt;/sup&gt;=0.88)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#2E86AB;strokeWidth=1;fontSize=8;" vertex="1" parent="1">
          <mxGeometry x="845" y="490" width="45" height="25" as="geometry" />
        </mxCell>
        <mxCell id="policy-more" value="..." style="text;fontSize=12;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="830" y="525" width="20" height="15" as="geometry" />
        </mxCell>

        <!-- 性能加权融合 -->
        <mxCell id="weighted-fusion" value="Performance-Weighted&lt;br&gt;Policy Fusion&lt;br&gt;&lt;br&gt;θ&lt;sub&gt;d&lt;/sub&gt;&lt;sup&gt;new&lt;/sup&gt; = (1-η)θ&lt;sub&gt;d&lt;/sub&gt;&lt;sup&gt;old&lt;/sup&gt; + η∑w&lt;sub&gt;j&lt;/sub&gt;θ&lt;sub&gt;j&lt;/sub&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FEF3E2;strokeColor=#F18F01;strokeWidth=2;fontSize=9;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="930" y="480" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- 更新后的域策略 -->
        <mxCell id="updated-domain" value="Updated Domain&lt;br&gt;Policy π&lt;sub&gt;θ&lt;sub&gt;d&lt;/sub&gt;&lt;/sub&gt;&lt;sup&gt;new&lt;/sup&gt;" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fillColor=#FEF3E2;strokeColor=#F18F01;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="940" y="590" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 域间知识传递 -->
        <mxCell id="inter-domain" value="Inter-Domain&lt;br&gt;Transfer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F8F0;strokeColor=#4CAF50;strokeWidth=1;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="780" y="590" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- ========== 连接箭头 ========== -->
        
        <!-- 面板A主流程箭头 -->
        <mxCell id="arrow-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="env-input" target="gat-module">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#4CAF50;endArrow=classic;endFill=1;" edge="1" parent="1" source="gat-module" target="coord-state">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="coord-state" target="agent-mind">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="agent-mind" target="environment">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#A4A4A4;endArrow=classic;endFill=1;" edge="1" parent="1" source="environment" target="reward">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#A4A4A4;endArrow=classic;endFill=1;" edge="1" parent="1" source="reward" target="experience-memory">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 双通道学习箭头 -->
        <mxCell id="arrow-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="experience-memory" target="rl-channel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#F18F01;endArrow=classic;endFill=1;" edge="1" parent="1" source="strategy-domain" target="distill-channel">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#333333;endArrow=classic;endFill=1;" edge="1" parent="1" source="rl-channel" target="total-loss">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#333333;endArrow=classic;endFill=1;" edge="1" parent="1" source="distill-channel" target="total-loss">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 反馈箭头 -->
        <mxCell id="arrow-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="total-loss" target="agent-mind">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 宏观闭环箭头 -->
        <mxCell id="arrow-12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#F18F01;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="agent-mind" target="performance-scorer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-13" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#F18F01;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="performance-scorer" target="domain-evolution">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-14" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#F18F01;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="domain-evolution" target="strategy-domain">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 面板B箭头 -->
        <mxCell id="arrow-b1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="task-queue-input" target="transformer-encoder">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-b2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="transformer-encoder" target="context-embed">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-b3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#4CAF50;endArrow=classic;endFill=1;" edge="1" parent="1" source="context-embed" target="autoregressive-decoder">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 自回归箭头 -->
        <mxCell id="arrow-b4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#4CAF50;endArrow=classic;endFill=1;" edge="1" parent="1" source="decode-t1" target="decode-t2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-b5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#4CAF50;endArrow=classic;endFill=1;" edge="1" parent="1" source="decode-t2" target="decode-t3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-b6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#F44336;endArrow=classic;endFill=1;" edge="1" parent="1" source="decode-t3" target="action-mask">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-b7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="autoregressive-decoder" target="complete-schedule">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 面板C箭头 -->
        <mxCell id="arrow-c1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="policy-j" target="weighted-fusion">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-c2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#2E86AB;endArrow=classic;endFill=1;" edge="1" parent="1" source="policy-k" target="weighted-fusion">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-c3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#F18F01;endArrow=classic;endFill=1;" edge="1" parent="1" source="weighted-fusion" target="updated-domain">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="arrow-c4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=1;strokeColor=#4CAF50;endArrow=classic;endFill=1;dashed=1;" edge="1" parent="1" source="inter-domain" target="updated-domain">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 时间尺度标注 -->
        <mxCell id="time-fast" value="Fast Loop&lt;br&gt;(每轮更新)" style="text;fontSize=8;fontColor=#666666;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="520" y="450" width="70" height="25" as="geometry" />
        </mxCell>

        <mxCell id="time-slow" value="Slow Loop&lt;br&gt;(域切换时)" style="text;fontSize=8;fontColor=#666666;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="280" y="290" width="70" height="25" as="geometry" />
        </mxCell>

        <!-- 图例 -->
        <mxCell id="legend" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAFAFA;strokeColor=#CCCCCC;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="480" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="legend-title" value="Legend" style="text;fontSize=12;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="60" y="490" width="50" height="15" as="geometry" />
        </mxCell>
        <mxCell id="legend-agent" value="■ Individual Intelligence" style="text;fontSize=9;fontColor=#2E86AB;" vertex="1" parent="1">
          <mxGeometry x="60" y="510" width="120" height="15" as="geometry" />
        </mxCell>
        <mxCell id="legend-domain" value="■ Collective Wisdom" style="text;fontSize=9;fontColor=#F18F01;" vertex="1" parent="1">
          <mxGeometry x="60" y="530" width="120" height="15" as="geometry" />
        </mxCell>
        <mxCell id="legend-coop" value="■ Cooperation Mechanism" style="text;fontSize=9;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="60" y="550" width="140" height="15" as="geometry" />
        </mxCell>
        <mxCell id="legend-env" value="■ Environment & Data" style="text;fontSize=9;fontColor=#A4A4A4;" vertex="1" parent="1">
          <mxGeometry x="60" y="570" width="120" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>